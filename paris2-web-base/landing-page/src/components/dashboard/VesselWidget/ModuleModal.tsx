import React from 'react';
import classNames from 'classnames';
import '../styles/scss/VesselModule.scss';

interface ModuleModalProps {
  isOpen: boolean;
  onClose: () => void;
  size: {
    width: string;
    height: string;
  };
  children: React.ReactNode;
}

/**
 * A generic, reusable modal component.
 */
export const ModuleModal: React.FC<ModuleModalProps> = ({ isOpen, onClose, size, children }) => {
  if (!isOpen) {
    return null;
  }

  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    // Close the modal only if the click is on the overlay itself, not its children
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <button
      type="button"
      className="ra-modal-overlay"
      onClick={handleOverlayClick}
      aria-label="Close modal"
    >
      <div
        className={classNames('ra-modal-content')}
        style={{
          '--modal-width': size?.width,
          '--modal-height': size?.height,
        }}
        onClick={(e) => e.stopPropagation()} // Prevent click from bubbling to overlay
      >
        {children}
      </div>
    </button>
  );
};
