import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>p, <PERSON>Down, Chev<PERSON>UpDown } from 'lucide-react';
import classNames from 'classnames';
import { SortConfig, Vessel, VesselTableProps } from '../../../types/types';
import { useInfiniteScroll } from '../../../hooks/useInfiniteScroll';
import Spinner from './Spinner';
import styles from '../styles/scss/VesselTable.module.scss';


const ExternalLinkIcon: React.FC<{ size?: number }> = ({ size = 20 }) => (
  <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M15.3337 17.7987H4.66699C4.13656 17.7987 3.62785 17.588 3.25278 17.213C2.87771 16.8379 2.66699 16.3292 2.66699 15.7987V5.13208C2.66699 4.60165 2.87771 4.09294 3.25278 3.71787C3.62785 3.34279 4.13656 3.13208 4.66699 3.13208H8.66699C8.8438 3.13208 9.01337 3.20232 9.1384 3.32734C9.26342 3.45237 9.33366 3.62194 9.33366 3.79875C9.33366 3.97556 9.26342 4.14513 9.1384 4.27015C9.01337 4.39518 8.8438 4.46541 8.66699 4.46541H4.66699C4.49018 4.46541 4.32061 4.53565 4.19559 4.66068C4.07056 4.7857 4.00033 4.95527 4.00033 5.13208V15.7987C4.00033 15.9756 4.07056 16.1451 4.19559 16.2702C4.32061 16.3952 4.49018 16.4654 4.66699 16.4654H15.3337C15.5105 16.4654 15.68 16.3952 15.8051 16.2702C15.9301 16.1451 16.0003 15.9756 16.0003 15.7987V11.7987C16.0003 11.6219 16.0706 11.4524 16.1956 11.3273C16.3206 11.2023 16.4902 11.1321 16.667 11.1321C16.8438 11.1321 17.0134 11.2023 17.1384 11.3273C17.2634 11.4524 17.3337 11.6219 17.3337 11.7987V15.7987C17.3337 16.3292 17.1229 16.8379 16.7479 17.213C16.3728 17.588 15.8641 17.7987 15.3337 17.7987ZM8.66699 12.4654C8.57925 12.4659 8.49228 12.4491 8.41105 12.4159C8.32983 12.3828 8.25595 12.3339 8.19366 12.2721C8.13117 12.2101 8.08158 12.1364 8.04773 12.0551C8.01389 11.9739 7.99646 11.8868 7.99646 11.7987C7.99646 11.7107 8.01389 11.6236 8.04773 11.5424C8.08158 11.4611 8.13117 11.3874 8.19366 11.3254L15.0603 4.46541H12.667C12.4902 4.46541 12.3206 4.39518 12.1956 4.27015C12.0706 4.14513 12.0003 3.97556 12.0003 3.79875C12.0003 3.62194 12.0706 3.45237 12.1956 3.32734C12.3206 3.20232 12.4902 3.13208 12.667 3.13208H16.667C16.8438 3.13208 17.0134 3.20232 17.1384 3.32734C17.2634 3.45237 17.3337 3.62194 17.3337 3.79875V7.79875C17.3337 7.97556 17.2634 8.14513 17.1384 8.27015C17.0134 8.39518 16.8438 8.46541 16.667 8.46541C16.4902 8.46541 16.3206 8.39518 16.1956 8.27015C16.0706 8.14513 16.0003 7.97556 16.0003 7.79875V5.40541L9.14033 12.2721C9.07803 12.3339 9.00415 12.3828 8.92293 12.4159C8.8417 12.4491 8.75473 12.4659 8.66699 12.4654Z"
      fill="#1F4A70"
    />
  </svg>
);

function hexToRgb(hex: string): string {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result
    ? `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}`
    : '128, 128, 128';
}

// New sub-components to be added to your file
const TruncatedCell: React.FC<{ value: string | number }> = ({ value }) => {
  const str = value.toString();
  const truncated = str.length > 15 ? `${str.slice(0, 15)}...` : str;
  return (
    <span className={styles['ra-vesselSecondColumnEntry']} title={str}>
      {truncated}
    </span>
  );
};

const StatusBadgeCell: React.FC<{ value: string | number; badgeColors: string[] }> = ({
  value,
  badgeColors,
}) => {
  const statusColors: { [key: string]: string } = {
    Critical: badgeColors[0] || '#f44336',
    Special: badgeColors[1] || '#fbc02d',
    Unassigned: badgeColors[2] || '#808080',
  };
  const selectedColor = statusColors[value as string] || '#808080';
  const cellStyle = { '--status-rgb': hexToRgb(selectedColor) } as React.CSSProperties;
  return (
    <span className={styles['ra-statusBadge']} style={cellStyle}>
      {value.toString()}
    </span>
  );
};

const ApprovalStatusCell: React.FC<{ value: string | number; badgeColors: string[] }> = ({
  value,
  badgeColors,
}) => {
  const statusMap: { [key: string]: string } = {
    Approved: styles['ra-approvedBadge'],
    'Approved with Condition': styles['ra-approvedWithConditionBadge'],
    Rejected: styles['ra-rejectedBadge'],
  };
  const badgeClass = statusMap[value as string];
  const badgeClasses = classNames(styles['ra-statusBadge'], badgeClass);

  let color: string | undefined;
  if (value === 'Pending') {
    color = badgeColors[1] || '#fbc02d';
  } else if (!badgeClass) {
    color = '#808080';
  }

  const cellStyle = color ? ({ '--status-rgb': hexToRgb(color) } as React.CSSProperties) : {};

  return (
    <span className={badgeClasses} style={cellStyle}>
      {value.toString()}
    </span>
  );
};

const DataCellContent: React.FC<{
  cellStyleType: 'default' | 'conditional';
  columnIndex: number;
  value: string | number;
  badgeColors: string[];
}> = React.memo(({ cellStyleType, columnIndex, value, badgeColors }) => {
  if (cellStyleType === 'conditional') {
    switch (columnIndex) {
      case 0:
        return <TruncatedCell value={value} />;
      case 1:
        return <StatusBadgeCell value={value} badgeColors={badgeColors} />;
      case 2:
        return <ApprovalStatusCell value={value} badgeColors={badgeColors} />;
      default:
        return null;
    }
  }

  // This part remains the same, as it's already simple
  const defaultBadgeColor = badgeColors[columnIndex] || '#808080';
  const badgeStyle = {
    '--badge-bg-color': defaultBadgeColor,
    '--badge-text-color': ['#fbc02d', '#ffeb3b'].includes(defaultBadgeColor.toLowerCase())
      ? 'black'
      : 'white',
  } as React.CSSProperties;

  return (
    <span className={styles['ra-badge']} style={badgeStyle}>
      {value.toString()}
    </span>
  );
});

const VesselTableHeader: React.FC<{
  headers: string[];
  sortConfig: SortConfig;
  onSort: (key: string) => void;
}> = React.memo(({ headers, sortConfig, onSort }) => {
  const getSortIcon = (headerKey: string) => {
    if (!sortConfig || sortConfig.key !== headerKey) {
      return (
        <ChevronsUpDown size={14} className={classNames(styles['ra-sortIcon'], styles.neutral)} />
      );
    }
    return sortConfig.direction === 'ascending' ? (
      <ArrowUp size={14} className={classNames(styles['ra-sortIcon'], styles.active)} />
    ) : (
      <ArrowDown size={14} className={classNames(styles['ra-sortIcon'], styles.active)} />
    );
  };

  return (
    <thead className={styles['ra-tableHeader']}>
      <tr>
        {headers.map((header) => (
          <th
            key={header} // Use the unique header name as the key
            className={classNames({ [styles['ra-textLeft']]: headers.indexOf(header) === 0 })}
            onClick={() => onSort(header)}
          >
            <div className={styles['ra-headerContent']}>
              {header}
            </div>
          </th>
        ))}
        <th key="actions" className={classNames(styles['ra-actionHeader'], styles.nonSortable)}>
          <div className={styles['ra-headerContent']}>Action</div>
        </th>
      </tr>
    </thead>
  );
});

// --- Sub-component: Table Row (FIXED) ---
const VesselTableRow: React.FC<{
  vessel: Vessel;
  badgeColors: string[];
  onSendEmail: (vessel: Vessel) => void;
  onVesselClick: (vessel: Vessel) => void;
  cellStyleType: 'default' | 'conditional';
}> = React.memo(({ vessel, badgeColors, onSendEmail, onVesselClick, cellStyleType }) => (
  <tr className={styles['ra-tableRow']}>
    {/* Column 1: Vessel Name is rendered separately first */}
    <td className={styles['ra-vesselNameCell']}>
      <button onClick={() => onVesselClick(vessel)} className={styles['ra-vesselNameButton']}>
        {vessel.name}
      </button>
    </td>

    {/* Subsequent columns are mapped from the vesselData array */}
    {vessel.vesselData.map((value, i) => (
      <td key={`${vessel.vessel_id}-${i}`}>
        <DataCellContent
          cellStyleType={cellStyleType}
          columnIndex={i}
          value={value}
          badgeColors={badgeColors}
        />
      </td>
    ))}

    {/* Final Column: Actions */}
    <td className={styles['ra-emailCell']}>
      <div className={styles['ra-emailButtonWrapper']}>
        <button onClick={() => onSendEmail(vessel)} className={styles['ra-emailButton']}>
          {cellStyleType === 'conditional' ? <ExternalLinkIcon /> : <MailIcon size={20} />}
        </button>
        <div className={styles['ra-tooltip']}>
          {cellStyleType === 'conditional' ? 'View Details' : 'Send Email'}
        </div>
      </div>
    </td>
  </tr>
));

// --- Main Component ---
export default function VesselTable({
  vessels,
  tableHeaders,
  badgeColors,
  onSendEmail,
  onVesselClick,
  isFetchingNextPage,
  isLoading,
  fetchNextPage,
  pagination,
  cellStyleType = 'default',
  sortConfig,
  onSort,
}: Readonly<VesselTableProps>) {
  const hasNextPage = pagination && pagination.page < pagination.totalPages;
  const dataHeaders = tableHeaders.filter((h) => h.toLowerCase() !== 'action');
  const { containerRef, handleScroll } = useInfiniteScroll({
    fetchNextPage,
    isFetchingNextPage,
    hasNextPage: !!hasNextPage,
    dataLength: vessels?.length || 0,
  });

  const renderBody = () => {
    if (isLoading) {
      return (
        <tr>
          <td colSpan={dataHeaders.length + 1} className={styles['ra-statusCell']}>
            <Spinner />
          </td>
        </tr>
      );
    }
    if (vessels.length === 0) {
      return (
        <tr>
          <td colSpan={dataHeaders.length + 1} className={styles['ra-statusCell']}>
            No results found
          </td>
        </tr>
      );
    }
    return vessels.map((vessel) => (
      <VesselTableRow
        key={vessel.vessel_id}
        vessel={vessel}
        badgeColors={badgeColors}
        onSendEmail={onSendEmail}
        onVesselClick={onVesselClick}
        cellStyleType={cellStyleType}
      />
    ));
  };

  return (
    <div ref={containerRef} onScroll={handleScroll} className={styles['ra-tableContainer']}>
      <table className={styles['ra-table']}>
        <VesselTableHeader headers={dataHeaders} sortConfig={sortConfig} onSort={onSort} />
        <tbody>{renderBody()}</tbody>
      </table>
      {isFetchingNextPage && (
        <div className={styles['ra-spinnerContainer']}>
          <Spinner />
        </div>
      )}
    </div>
  );
}
