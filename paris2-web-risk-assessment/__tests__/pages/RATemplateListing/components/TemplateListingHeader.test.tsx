import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { useNavigate } from 'react-router-dom';
import { TemplateListingHeader } from '../../../../src/pages/RATemplateListing/components/TemplateListingHeader';
import { useDataStoreContext } from '../../../../src/context';

// Mock dependencies
const mockNavigate = jest.fn();

jest.mock('react-router-dom', () => ({
  useNavigate: jest.fn(),
}));

jest.mock('../../../../src/context', () => ({
  useDataStoreContext: jest.fn(),
}));

jest.mock('../../../../src/components/icons', () => ({
  PlusIcon: () => <span data-testid="mock-plus-icon">+</span>,
}));

jest.mock('react-bootstrap', () => {
  const actual = jest.requireActual('react-bootstrap');
  return {
    ...actual,
    Button: (props: any) => <button {...props}>{props.children}</button>,
  };
});

jest.mock('classnames', () => (...args: any[]) => args.filter(Boolean).join(' '));

describe('TemplateListingHeader', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useNavigate as jest.Mock).mockReturnValue(mockNavigate);
  });

  it('renders breadcrumb and create new button', () => {
    (useDataStoreContext as jest.Mock).mockReturnValue({
      roleConfig: { riskAssessment: { canAddTemplate: true } }
    });

    render(<TemplateListingHeader className="test-class" />);

    expect(screen.getByText('Risk Assessment')).toBeInTheDocument();
    // The breadcrumb is split: "/" is inside a div with spaces, so match flexibly
    const breadcrumbMatches = screen.getAllByText((content, node) => {
      return !!node?.textContent && node.textContent.replace(/\s/g, '').includes('/Templates');
    });
    expect(breadcrumbMatches.length).toBeGreaterThan(0);
    expect(screen.getByText('Create New')).toBeInTheDocument();
    expect(screen.getByTestId('mock-plus-icon')).toBeInTheDocument();
  });

  it('does not render create new button if permission is false', () => {
    (useDataStoreContext as jest.Mock).mockReturnValue({
      roleConfig: { riskAssessment: { canAddTemplate: false } }
    });

    render(<TemplateListingHeader />);

    expect(screen.queryByText('Create New')).not.toBeInTheDocument();
  });

  it('navigates to /risk-assessment when breadcrumb button is clicked', () => {
    (useDataStoreContext as jest.Mock).mockReturnValue({
      roleConfig: { riskAssessment: { canAddTemplate: true } }
    });

    render(<TemplateListingHeader />);

    fireEvent.click(screen.getByText('Risk Assessment'));
    expect(mockNavigate).toHaveBeenCalledWith('/risk-assessment');
  });

  it('navigates to /risk-assessment/templates/create when create new is clicked', () => {
    (useDataStoreContext as jest.Mock).mockReturnValue({
      roleConfig: { riskAssessment: { canAddTemplate: true } }
    });

    render(<TemplateListingHeader />);

    fireEvent.click(screen.getByText('Create New'));
    expect(mockNavigate).toHaveBeenCalledWith('/risk-assessment/templates/create');
  });
});
