@import '~react-datepicker/dist/react-datepicker.css';
@import './variable';
@import './styles.scss';

// ###### App
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  font-family: $font-family;
  font-size: $root-font-size;
  height: 100%;
}

body {
  background-color: #ffffff;
  margin-top: 80px;
  color: $text-color;
  overflow-x: hidden;

  [id='single-spa-application:@paris2/risk-assessment'] {
    height: 100%;
    padding: 0px 15px;
    margin-top: 80px;
    padding-bottom: 20px;

    &:empty {
      height: auto;
      padding: 0px;
    }
  }

  a {
    color: $primary-color;
  }

  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #b8b8b8; /* Scrollbar thumb color */
    border-radius: 4px; /* Round the corners of the scrollbar thumb */
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #656565; /* Scrollbar thumb color on hover */
  }
}
