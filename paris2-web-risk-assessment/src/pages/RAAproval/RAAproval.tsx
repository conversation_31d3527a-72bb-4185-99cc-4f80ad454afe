import React, {useEffect, useMemo, useRef, useState} from 'react';
import {useNavigate, useParams} from 'react-router-dom';
import {toast} from 'react-toastify';
import * as _ from 'lodash';
import {Button} from 'react-bootstrap';
import PreviewFormDetails from '../CreateRA/PreviewFormDetails';
import {RiskForm} from '../../types';
import {TemplateForm} from '../../types/template';
import {useQuery} from '../../hooks/useQuery';
import {
  getRiskById,
  getTemplateById,
  setRiskRaLevel,
  updateSavedRA,
  getHazardsList,
  getMainRiskParameterType,
  getRiskCategoryList,
  getRiskParameterType,
  getTaskReliabilityAssessList,
  getApprovalsRequiredList,
  generatePDF,
} from '../../services/services';
import {
  calculateRiskRating,
  createRiskFormFromData,
  formParameterHand<PERSON>,
  groupRiskParameters,
} from '../../utils/helper';
import SearchDropdown from '../../components/SearchDropdown';
import {raLevels} from '../RAListing/components/RAFilters';
import {RaLevel, RAStatus} from '../../enums';
import {
  CategoryItem,
  compareTemplateItems,
  getErrorMessage,
  getRiskRatingValue,
  HazardItem,
  parseDate,
  raStatusLabelToValue,
} from '../../utils/common';
import Loader from '../../components/Loader';
import {useDataStoreContext} from '../../context';
import SubmitRoutineRAModal from '../CreateRA/SubmitRoutineRAModal';
import {ExitPageModal} from '../../components/ExitPageModal';

import '../../styles/components/ra-approval.scss';

export default function RAAproval() {
  const params = useParams<{id: string}>();
  const raId = String(params.id);

  const {
    setDataStore,
    roleConfig,
    roleConfig: {user, riskAssessment: {canExportPDF} = {canExportPDF: false}},
  } = useDataStoreContext();
  const [isLoading, setIsLoading] = useState(true);
  const [form, setForm] = useState<RiskForm | null>(null);
  const [originalForm, setOriginalForm] = useState<RiskForm | null>(null);
  const [levelOfRA, setLevelOfRA] = useState<RaLevel | undefined>(undefined);
  const atRiskRef = useRef<any>(null);
  const [showExitModal, setShowExitModal] = useState(false);
  const [isBlocking, setIsBlocking] = useState(false);

  const navigate = useNavigate();

  // Function to check if specific fields have changed
  const hasFormChanges = useMemo(() => {
    if (!form || !originalForm) return false;

    // Check the specific fields mentioned in the requirements
    const fieldsToCheck = [
      'task_alternative_consideration',
      'task_rejection_reason',
      'worst_case_scenario',
      'recovery_measures',
    ];

    // Check if any of the text fields have changed
    const textFieldsChanged = fieldsToCheck.some(
      field =>
        form[field as keyof RiskForm] !== originalForm[field as keyof RiskForm],
    );

    // Check if risk_task_reliability_assessment has changed
    const assessmentChanged = !_.isEqual(
      form.risk_task_reliability_assessment,
      originalForm.risk_task_reliability_assessment,
    );

    return textFieldsChanged || assessmentChanged;
  }, [form, originalForm]);

  const previewOnly = useMemo(() => {
    if (!form) return true;

    const isPublished =
      raStatusLabelToValue[form.status] === RAStatus.PUBLISHED;
    const canApprove = roleConfig?.riskAssessment?.canApproveRisk === true;

    if (!isPublished || !canApprove) {
      return true;
    }

    const pendingApprovers = form.risk_approver?.filter(
      approver => approver.approval_status === null,
    );

    const isUserOneOfTheApprovers = pendingApprovers?.some(
      approver => approver.keycloak_id === user?.user_id,
    );

    return !isUserOneOfTheApprovers;
  }, [form]);

  const {
    data: raData,
    isLoading: isLoadingRa,
    isError,
    error,
    refetch,
  } = useQuery(['risk', raId], () => getRiskById(raId), {
    enabled: !!raId,
    onSuccess: data => {
      if (data?.result) {
        const formData = createRiskFormFromData(data.result);
        setForm(formData);
        setOriginalForm(_.cloneDeep(formData));
      }
    },
  });

  const templateId = raData?.result?.template_id;
  const templateQ = useQuery(
    ['template', templateId],
    () => getTemplateById(String(templateId)),
    {enabled: Boolean(templateId)},
  );

  useEffect(() => {
    const loadBasicDetails = async () => {
      try {
        setIsLoading(true);
        const results = await Promise.allSettled([
          getRiskCategoryList(),
          getHazardsList(),
          getRiskParameterType(),
          getTaskReliabilityAssessList(),
          getMainRiskParameterType(),
          getMainRiskParameterType(true),
          getApprovalsRequiredList(1),
          getApprovalsRequiredList(2),
        ]);
        const [
          categorListData,
          hazardsListData,
          riskParameterData,
          taskRelAssessData,
          mainRiskParameterData,
          mainRiskParameterDataForRiskRating,
          approvalListOffice,
          approvalListVessel,
        ] = results.map(result =>
          result.status === 'fulfilled' ? result.value : [],
        );

        const groupedRiskParameterData = groupRiskParameters(riskParameterData);

        setDataStore((prev: any) => ({
          ...prev,
          riskCategoryList: categorListData,
          hazardsList: hazardsListData,
          riskParameterType: groupedRiskParameterData,
          taskReliabilityAssessList: taskRelAssessData,
          riskParameterList: mainRiskParameterData,
          riskParameterListForRiskRaiting: mainRiskParameterDataForRiskRating,
          approversReqListForRiskOffice: approvalListOffice,
          approversReqListForRiskVessel: approvalListVessel,
        }));
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setIsLoading(false);
      }
    };
    loadBasicDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Update isBlocking whenever previewOnly changes
  useEffect(() => {
    setIsBlocking(!previewOnly);
  }, [previewOnly]);

  // Set up beforeunload listener
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (isBlocking) {
        event.preventDefault();
        event.returnValue = '';
        return '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [isBlocking]);

  // Handle breadcrumb click
  const handleBreadcrumbClick = (event: React.MouseEvent, link: string) => {
    if (isBlocking && link === '/risk-assessment') {
      event.preventDefault();
      setShowExitModal(true);
    }
  };

  const $content = useMemo(() => {
    if (isLoadingRa || isLoading) {
      return <Loader isOverlayLoader />;
    }

    if (isError) {
      return <div>Error: {error?.message || 'Failed to load data.'}</div>;
    }

    if (!form) {
      return <div>No data found.</div>;
    }

    return null;
  }, [isLoadingRa, isLoading, isError, error, form]);

  const setRaLevel = async (
    risk_id: number,
    ra_level: RaLevel,
    actionDate?: Date,
  ) => {
    if (!levelOfRA || !raId || (levelOfRA === RaLevel.ROUTINE && !actionDate)) {
      toast.info('Please select a valid RA level and action date.');
      return;
    }
    setForm(prev => ({...prev, ra_level: levelOfRA} as RiskForm));
    try {
      const result = await setRiskRaLevel({
        ra_level,
        risk_id,
        approval_date: parseDate(actionDate) || undefined,
      });
      if (levelOfRA !== RaLevel.ROUTINE)
        toast.success('RA level Saved Successfully!');

      refetch();
      return result;
    } catch (error) {
      toast.error(getErrorMessage(error));
      throw error;
    }
  };
  const handelGenratePDF = async (id: number | string) => {
    try {
      setIsLoading(true);
      await generatePDF(id);
      setIsLoading(false);
      toast.info(
        `Your RA PDF is being processed and will be sent to your registered email address shortly.`,
      );
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast.error(`Error generating PDF`);
      setIsLoading(false);
    }
  };
  const updateRaDetails = async (formToSave?: RiskForm | TemplateForm) => {
    setIsLoading(true);
    const formData = formToSave || form;
    const riskRating = formData ? calculateRiskRating(formData) : undefined;
    try {
      await updateSavedRA(
        Number(raId),
        formParameterHandler({
          ...formData,
          risk_rating: riskRating ? getRiskRatingValue(riskRating) : undefined,
        }),
      );
      toast.success(`Risk form updated successfully`);
      // Update the original form to reflect the saved state
      setOriginalForm(_.cloneDeep(formData as RiskForm));
    } catch (err) {
      console.error('Error publishing template:', err);
    } finally {
      setIsLoading(false);
    }
    refetch();
  };

  const isCategoriesSameAsTemplate = compareTemplateItems<CategoryItem>(
    raData?.result?.risk_category,
    templateQ.data?.result?.template_category,
    item => Boolean(item.category_is_other),
    item => item.category?.id ?? null,
  );

  const isHazardsSameAsTemplate = compareTemplateItems<HazardItem>(
    raData?.result?.risk_hazards,
    templateQ.data?.result?.template_hazards,
    item => item.hazard_category_is_other,
    item => item.hazard_detail?.id ?? null,
  );

  const defaultRiskApprover = raData?.result?.risk_approver?.find(
    approver =>
      [0, 4].includes(approver.status) && approver.approval_order === null,
  );
  const showLevelOfRADropdown =
    raData &&
    !raData.result.ra_level &&
    defaultRiskApprover?.keycloak_id === user.user_id;
    console.log("🚀 ~ RAAproval ~ defaultRiskApprover:", defaultRiskApprover)
    console.log("🚀 ~ RAAproval ~ raData:", raData)
  console.log("🚀 ~ RAAproval ~ showLevelOfRADropdown:", showLevelOfRADropdown)
  const isApproved =
    raData?.result &&
    raData?.result?.status &&
    [
      RAStatus.APPROVED,
      RAStatus.APPROVED_WITH_CONDITION,
      RAStatus.REJECTED,
    ].includes(raData?.result?.status) &&
    canExportPDF;

  return (
    <div className="ra-approval-page">
      {$content || (
        <PreviewFormDetails
          raId={Number(raId)}
          type="risk"
          form={form as unknown as RiskForm}
          setForm={setForm}
          atRiskRef={atRiskRef}
          handlePreviewPublush={updateRaDetails}
          // eslint-disable-next-line @typescript-eslint/no-empty-function
          handleSaveToDraft={() => {}}
          isCategoriesSameAsTemplate={isCategoriesSameAsTemplate}
          isHazardsSameAsTemplate={isHazardsSameAsTemplate}
          riskApprover={raData?.result?.risk_approver}
          refechRA={refetch}
          breadcrumbOptions={{
            items: [
              {
                title: 'Risk Assessment',
                link: '/risk-assessment',
                onClick: handleBreadcrumbClick,
              },
              {title: form?.task_requiring_ra || ''}, // No link, just text
            ],
            options: (
              <div className="d-flex align-items-center">
                {showLevelOfRADropdown ? (
                  <div className="d-flex align-items-center justify-content-end">
                    <SearchDropdown
                      placeholder="Set Level of R.A."
                      className="ra-approval-status-dropdown"
                      options={raLevels.filter(
                        item => item.value !== RaLevel.LEVEL_1_RA,
                      )}
                      selected={levelOfRA ? [levelOfRA] : null}
                      onChange={value => {
                        if (value && value.length > 0) {
                          setLevelOfRA(Number(value[0]));
                        } else {
                          setLevelOfRA(undefined);
                        }
                      }}
                      multiple={false}
                      hideSearch
                    />
                    {levelOfRA === RaLevel.ROUTINE ? (
                      <SubmitRoutineRAModal
                        trigger={
                          <button
                            disabled={!levelOfRA}
                            className="ra-approval-save-btn"
                          >
                            Save
                          </button>
                        }
                        onConfirm={params =>
                          setRaLevel(
                            Number(raId),
                            levelOfRA,
                            params.approveDate,
                          )
                        }
                      />
                    ) : (
                      <button
                        disabled={!levelOfRA}
                        className="ra-approval-save-btn"
                        onClick={() =>
                          setRaLevel(
                            Number(raId),
                            levelOfRA as unknown as RaLevel,
                          )
                        }
                      >
                        Save
                      </button>
                    )}
                  </div>
                ) : undefined}
                {isApproved && (
                  <div className="d-flex align-items-center justify-content-end">
                    <Button
                      variant="outline-primary"
                      className="me-2 basic-btn"
                      onClick={() => {
                        handelGenratePDF(raId);
                      }}
                    >
                      Export PDF
                    </Button>
                  </div>
                )}
              </div>
            ),
          }}
          bottomButtonConfig={[
            {
              title: 'Cancel',
              testID: 'form-ra-approval-cancel-btn',
              variant: 'secondary',
              customClass: 'sec-btn fs-14 w-290p',
              onClick: () => {
                if (previewOnly) navigate('/risk-assessment');
                else setShowExitModal(true);
              },
            },
            {
              title: 'Save',
              testID: 'form-ra-approval-save-btn',
              variant: 'primary',
              customClass: 'primary-btn fs-14 w-290p',
              onClick: async () => {
                await updateRaDetails();
              },
              disabled: isLoading || !hasFormChanges,
            },
          ]}
          previewOnly={previewOnly}
          showBreadCrumb
          allowSaveOnModalClose
        />
      )}

      {showExitModal && !previewOnly && (
        <ExitPageModal
          onClose={() => setShowExitModal(false)}
          onConfirm={() => navigate('/risk-assessment')}
        />
      )}
    </div>
  );
}
